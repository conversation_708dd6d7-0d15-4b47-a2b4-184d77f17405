<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const themeStore = useThemeStore()

// 初始化主题和认证状态
onMounted(() => {
  // 初始化主题
  themeStore.initTheme()

  // 检查并刷新token
  if (authStore.isAuthenticated) {
    authStore.checkAndRefreshToken()
  }
})

const isCollapsed = ref(false)

// 菜单项配置
const menuItems = [
  {
    key: 'dashboard',
    title: '仪表板',
    icon: 'tachometer-alt',
    path: '/admin/dashboard',
  },
  {
    key: 'users',
    title: '用户管理',
    icon: 'users',
    path: '/admin/users',
  },
  {
    key: 'domains',
    title: '域名管理',
    icon: 'globe',
    path: '/admin/domains',
  },
  {
    key: 'emails',
    title: '邮件审查',
    icon: 'envelope-open',
    path: '/admin/emails',
  },
  {
    key: 'logs',
    title: '日志审计',
    icon: 'file-alt',
    path: '/admin/logs',
  },
  {
    key: 'redeem-codes',
    title: '兑换码管理',
    icon: 'ticket-alt',
    path: '/admin/redeem-codes',
  },
]

// 当前激活的菜单项
const activeMenuItem = computed(() => {
  const currentPath = route.path
  return menuItems.find((item) => currentPath.startsWith(item.path))?.key || 'dashboard'
})

// 当前激活的菜单路径（用于el-menu）
const activeMenuPath = computed(() => {
  const currentPath = route.path
  return menuItems.find((item) => currentPath.startsWith(item.path))?.path || '/admin/dashboard'
})

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 菜单选择处理
const handleMenuSelect = (path: string) => {
  router.push(path)
}

// 退出登录
const logout = async () => {
  await authStore.logout()
  router.push('/login')
}

// 返回用户端
const goToUserDashboard = () => {
  router.push('/dashboard')
}
</script>

<template>
  <div id="admin-layout" class="admin-layout min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 侧边栏 -->
    <aside
      :class="[
        'fixed inset-y-0 left-0 z-50 flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 shadow-lg',
        isCollapsed ? 'w-16' : 'w-280',
      ]"
    >
      <!-- 侧边栏头部 -->
      <div
        class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700"
      >
        <div v-if="!isCollapsed" class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="shield-alt" class="text-white text-sm" />
          </div>
          <span class="text-lg font-semibold text-gray-900 dark:text-gray-100"> 管理后台 </span>
        </div>
        <el-button
          @click="toggleSidebar"
          text
          class="!p-2"
        >
          <font-awesome-icon
            :icon="isCollapsed ? 'chevron-right' : 'chevron-left'"
            class="text-gray-500 dark:text-gray-400"
          />
        </el-button>
      </div>

      <!-- 用户信息区域 -->
      <div v-if="!isCollapsed" class="p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="user" class="text-white text-sm" />
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ authStore.user?.email }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">管理员</div>
          </div>
          <el-button
            @click="themeStore.toggleTheme"
            text
            class="!p-2"
            :title="themeStore.isDark ? '切换到亮色模式' : '切换到暗色模式'"
          >
            <font-awesome-icon
              :icon="themeStore.isDark ? 'sun' : 'moon'"
              class="text-gray-500 dark:text-gray-400"
            />
          </el-button>
        </div>
      </div>

      <!-- 主菜单 -->
      <div class="flex-1 overflow-y-auto">
        <el-menu
          :default-active="activeMenuPath"
          :collapse="isCollapsed"
          :unique-opened="true"
          background-color="transparent"
          text-color="var(--el-text-color-primary)"
          active-text-color="var(--el-color-primary)"
          @select="handleMenuSelect"
          class="border-none"
        >
          <el-menu-item
            v-for="item in menuItems"
            :key="item.key"
            :index="item.path"
          >
            <template #title>
              <font-awesome-icon :icon="item.icon" class="mr-3" />
              <span>{{ item.title }}</span>
            </template>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 侧边栏底部操作 -->
      <div class="p-3 border-t border-gray-200 dark:border-gray-700">
        <div v-if="!isCollapsed" class="space-y-2">
          <el-button
            @click="goToUserDashboard"
            text
            class="w-full justify-start !text-gray-700 dark:!text-gray-300"
          >
            <font-awesome-icon icon="arrow-left" class="mr-3" />
            返回用户端
          </el-button>
          <el-button
            @click="logout"
            text
            type="danger"
            class="w-full justify-start"
          >
            <font-awesome-icon icon="sign-out-alt" class="mr-3" />
            退出登录
          </el-button>
        </div>
        <div v-else class="space-y-2">
          <el-button
            @click="goToUserDashboard"
            text
            class="w-full !text-gray-700 dark:!text-gray-300"
            title="返回用户端"
          >
            <font-awesome-icon icon="arrow-left" />
          </el-button>
          <el-button
            @click="logout"
            text
            type="danger"
            class="w-full"
            title="退出登录"
          >
            <font-awesome-icon icon="sign-out-alt" />
          </el-button>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div :class="['main-content', isCollapsed ? 'collapsed' : 'expanded']">
      <!-- 简化的顶部导航栏 -->
      <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16">
        <div class="flex items-center h-full px-6">
          <!-- 页面标题 -->
          <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {{ menuItems.find((item) => item.key === activeMenuItem)?.title || '管理后台' }}
          </h1>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6 w-full max-w-none">
        <router-view />
      </main>
    </div>
  </div>
</template>

<style scoped>
#admin-layout {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  background-color: #f9fafb;
  /* 确保管理后台布局完全独立 */
  position: relative;
  z-index: 1;
}

.dark #admin-layout {
  background-color: #111827;
}

.admin-layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
aside {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 50;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dark aside {
  background-color: #1f2937;
  border-right-color: #374151;
}

/* 主内容区域 */
.main-content {
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  background-color: #f9fafb;
}

.dark .main-content {
  background-color: #111827;
}

.main-content.expanded {
  margin-left: 280px; /* 280px 侧边栏宽度 */
}

.main-content.collapsed {
  margin-left: 64px; /* 64px 折叠侧边栏宽度 */
}

/* 针对1080p及更高分辨率设备优化，移除响应式代码 */
main {
  width: 100%;
  max-width: none;
  padding: 32px 40px;
  min-height: calc(100vh - 64px); /* 减去header高度 */
}

/* 确保内容区域有合适的最大宽度，但不限制在1280px */
main > * {
  max-width: 1600px; /* 提高到1600px，适合高分辨率显示器 */
  margin: 0 auto;
}

.main-content.collapsed {
  margin-left: 64px; /* 64px 折叠侧边栏宽度 */
}


.dark .main-content {
  background-color: #111827;
}

/* 确保内容区域有合适的最大宽度 */
main > * {
  max-width: 1400px;
  margin: 0 auto;
}

/* 自定义滚动条 */
.el-menu::-webkit-scrollbar {
  width: 4px;
}

.el-menu::-webkit-scrollbar-track {
  background: transparent;
}

.el-menu::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

.el-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Element Plus Menu 自定义样式 */
:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  margin: 4px 12px;
  border-radius: 8px;
  height: 44px;
  line-height: 44px;
}

:deep(.el-menu-item:hover) {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

/* 自定义宽度类 */
.w-280 {
  width: 280px;
}
</style>
